import { Locale } from "@/i18n-config";
import { getDictionary } from "@/dictionaries";
import UserManagementClient from "./UserManagementClient";

interface UserManagementPageProps {
  params: {
    lang: Locale;
  };
}

export default async function UserManagementPage({ params }: UserManagementPageProps) {
  const dict = await getDictionary(params.lang);

  return <UserManagementClient dict={dict} lang={params.lang} />;
}
