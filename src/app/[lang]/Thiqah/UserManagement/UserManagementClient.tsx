"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import { Users, Shield, Edit, Save, X } from "lucide-react";
import { auth } from "@/Firebase/Authentication/authConfig";
import { 
  getAllUsers, 
  getUserProfile, 
  updateUserRole, 
  UserRole, 
  UserProfile,
  isConsultant 
} from "@/Firebase/firestore/services/UserService";
import { useToast } from "@/components/ui/use-toast";
import { Dictionary } from "@/dictionaries";
import { Locale } from "@/i18n-config";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

interface UserManagementClientProps {
  dict: Dictionary;
  lang: Locale;
}

export default function UserManagementClient({ dict, lang }: UserManagementClientProps) {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [editingUser, setEditingUser] = useState<string | null>(null);
  const [newRole, setNewRole] = useState<UserRole | null>(null);
  const [currentUser, setCurrentUser] = useState<UserProfile | null>(null);
  const { toast } = useToast();
  const router = useRouter();
  const isRTL = lang === 'ar';

  useEffect(() => {
    const checkAccess = async () => {
      const user = auth.currentUser;
      if (!user) {
        router.push(`/${lang}/auth/signin`);
        return;
      }

      try {
        const userProfile = await getUserProfile(user.uid);
        setCurrentUser(userProfile);

        if (!userProfile || userProfile.role !== UserRole.CONSULTANT) {
          toast({
            title: isRTL ? "غير مصرح" : "Unauthorized",
            description: isRTL ? "ليس لديك صلاحية للوصول إلى إدارة المستخدمين" : "You don't have permission to access user management",
            variant: "destructive",
          });
          router.push(`/${lang}/Thiqah/Home`);
          return;
        }

        await loadUsers();
      } catch (error) {
        console.error("Error checking access:", error);
        toast({
          title: isRTL ? "خطأ" : "Error",
          description: isRTL ? "حدث خطأ في التحقق من الصلاحيات" : "Error checking permissions",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    checkAccess();
  }, [lang, router, toast, isRTL]);

  const loadUsers = async () => {
    try {
      const allUsers = await getAllUsers();
      setUsers(allUsers);
    } catch (error) {
      console.error("Error loading users:", error);
      toast({
        title: isRTL ? "خطأ" : "Error",
        description: isRTL ? "فشل في تحميل المستخدمين" : "Failed to load users",
        variant: "destructive",
      });
    }
  };

  const handleRoleUpdate = async (uid: string) => {
    if (!newRole) return;

    try {
      await updateUserRole(uid, newRole);
      await loadUsers(); // Reload users to reflect changes
      setEditingUser(null);
      setNewRole(null);
      
      toast({
        title: isRTL ? "تم التحديث" : "Updated",
        description: isRTL ? "تم تحديث دور المستخدم بنجاح" : "User role updated successfully",
        variant: "default",
      });
    } catch (error) {
      console.error("Error updating role:", error);
      toast({
        title: isRTL ? "خطأ" : "Error",
        description: isRTL ? "فشل في تحديث دور المستخدم" : "Failed to update user role",
        variant: "destructive",
      });
    }
  };

  const getRoleBadgeColor = (role: UserRole) => {
    switch (role) {
      case UserRole.CONSULTANT:
        return "bg-purple-500/20 text-purple-300 border-purple-300/50";
      case UserRole.CLIENT:
        return "bg-blue-500/20 text-blue-300 border-blue-300/50";
      case UserRole.VIEWER:
        return "bg-gray-500/20 text-gray-300 border-gray-300/50";
      default:
        return "bg-gray-500/20 text-gray-300 border-gray-300/50";
    }
  };

  const getRoleDisplayName = (role: UserRole) => {
    switch (role) {
      case UserRole.CONSULTANT:
        return isRTL ? "مستشار" : "Consultant";
      case UserRole.CLIENT:
        return isRTL ? "عميل" : "Client";
      case UserRole.VIEWER:
        return isRTL ? "مشاهد" : "Viewer";
      default:
        return role;
    }
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 flex items-center justify-center">
        <div className="text-white text-xl">
          {isRTL ? "جاري التحميل..." : "Loading..."}
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-900 via-purple-900 to-slate-900 p-6">
      <div className="max-w-7xl mx-auto">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          className="mb-8"
        >
          <div className="flex items-center gap-3 mb-2">
            <Users className="w-8 h-8 text-purple-400" />
            <h1 className="text-3xl font-bold text-white">
              {isRTL ? "إدارة المستخدمين" : "User Management"}
            </h1>
          </div>
          <p className="text-gray-300">
            {isRTL ? "إدارة أدوار المستخدمين وصلاحياتهم" : "Manage user roles and permissions"}
          </p>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <Card className="bg-white/10 backdrop-blur-sm border-white/20">
            <CardHeader>
              <CardTitle className="text-white flex items-center gap-2">
                <Shield className="w-5 h-5" />
                {isRTL ? "قائمة المستخدمين" : "Users List"}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {users.map((user) => (
                  <div
                    key={user.uid}
                    className="bg-white/5 rounded-lg p-4 border border-white/10"
                  >
                    <div className="flex items-center justify-between">
                      <div className="flex items-center gap-4">
                        <div>
                          <h3 className="text-white font-medium">
                            {user.displayName || user.email}
                          </h3>
                          <p className="text-gray-400 text-sm">{user.email}</p>
                          <p className="text-gray-500 text-xs">
                            {isRTL ? "تاريخ الإنشاء:" : "Created:"} {user.createdAt.toDate().toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      
                      <div className="flex items-center gap-3">
                        {editingUser === user.uid ? (
                          <div className="flex items-center gap-2">
                            <Select
                              value={newRole || user.role}
                              onValueChange={(value) => setNewRole(value as UserRole)}
                            >
                              <SelectTrigger className="w-32 bg-white/10 border-white/20 text-white">
                                <SelectValue />
                              </SelectTrigger>
                              <SelectContent>
                                {Object.values(UserRole).map((role) => (
                                  <SelectItem key={role} value={role}>
                                    {getRoleDisplayName(role)}
                                  </SelectItem>
                                ))}
                              </SelectContent>
                            </Select>
                            <Button
                              size="sm"
                              onClick={() => handleRoleUpdate(user.uid)}
                              className="bg-green-500/20 hover:bg-green-500/30 text-green-300"
                            >
                              <Save className="w-4 h-4" />
                            </Button>
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => {
                                setEditingUser(null);
                                setNewRole(null);
                              }}
                              className="bg-red-500/20 hover:bg-red-500/30 text-red-300 border-red-300/50"
                            >
                              <X className="w-4 h-4" />
                            </Button>
                          </div>
                        ) : (
                          <div className="flex items-center gap-2">
                            <Badge className={getRoleBadgeColor(user.role)}>
                              {getRoleDisplayName(user.role)}
                            </Badge>
                            {user.uid !== currentUser?.uid && (
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => {
                                  setEditingUser(user.uid);
                                  setNewRole(user.role);
                                }}
                                className="bg-blue-500/20 hover:bg-blue-500/30 text-blue-300 border-blue-300/50"
                              >
                                <Edit className="w-4 h-4" />
                              </Button>
                            )}
                          </div>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </motion.div>
      </div>
    </div>
  );
}
