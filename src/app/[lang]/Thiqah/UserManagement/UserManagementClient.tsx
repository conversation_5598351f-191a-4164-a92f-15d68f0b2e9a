"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { motion } from "framer-motion";
import {
  Users,
  Shield,
  Edit,
  Save,
  X,
  Plus,
  Trash2,
  User<PERSON>heck,
  UserX,
  Search,
  Filter,
  MoreVertical,
  Eye,
  EyeOff
} from "lucide-react";
import { auth } from "@/Firebase/Authentication/authConfig";
import {
  UserRole,
  UserProfile
} from "@/Firebase/firestore/services/UserService";
import { useToast } from "@/components/ui/use-toast";
import { Dictionary } from "@/dictionaries";
import { Locale } from "@/i18n-config";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import Image from "next/image";

interface UserManagementClientProps {
  dict: Dictionary;
  lang: Locale;
}

interface NewUserForm {
  email: string;
  displayName: string;
  role: UserRole;
  password: string;
}

export default function UserManagementClient({ dict, lang }: UserManagementClientProps) {
  const [users, setUsers] = useState<UserProfile[]>([]);
  const [filteredUsers, setFilteredUsers] = useState<UserProfile[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [roleFilter, setRoleFilter] = useState<string>("all");
  const [editingUser, setEditingUser] = useState<string | null>(null);
  const [newRole, setNewRole] = useState<UserRole | null>(null);
  const [currentUser, setCurrentUser] = useState<UserProfile | null>(null);
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [showDeleteDialog, setShowDeleteDialog] = useState<string | null>(null);
  const [newUserForm, setNewUserForm] = useState<NewUserForm>({
    email: "",
    displayName: "",
    role: UserRole.CLIENT,
    password: ""
  });
  const [actionLoading, setActionLoading] = useState<string | null>(null);
  const { toast } = useToast();
  const router = useRouter();
  const isRTL = lang === 'ar';

  // API Functions
  const getAuthToken = async () => {
    const user = auth.currentUser;
    if (!user) throw new Error('No authenticated user');
    return await user.getIdToken();
  };

  const fetchUsers = async () => {
    try {
      const token = await getAuthToken();
      const response = await fetch('/api/users', {
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        throw new Error('Failed to fetch users');
      }

      const data = await response.json();
      return data.users;
    } catch (error) {
      console.error('Error fetching users:', error);
      throw error;
    }
  };

  const createUser = async (userData: NewUserForm) => {
    try {
      const token = await getAuthToken();
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify(userData),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to create user');
      }

      return await response.json();
    } catch (error) {
      console.error('Error creating user:', error);
      throw error;
    }
  };

  const updateUserRole = async (uid: string, role: UserRole) => {
    try {
      const token = await getAuthToken();
      const response = await fetch('/api/users', {
        method: 'PUT',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ uid, role }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update user role');
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating user role:', error);
      throw error;
    }
  };

  const toggleUserStatus = async (uid: string, disabled: boolean) => {
    try {
      const token = await getAuthToken();
      const response = await fetch(`/api/users/${uid}`, {
        method: 'PATCH',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ disabled }),
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to update user status');
      }

      return await response.json();
    } catch (error) {
      console.error('Error updating user status:', error);
      throw error;
    }
  };

  const deleteUser = async (uid: string) => {
    try {
      const token = await getAuthToken();
      const response = await fetch(`/api/users/${uid}`, {
        method: 'DELETE',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json',
        },
      });

      if (!response.ok) {
        const error = await response.json();
        throw new Error(error.error || 'Failed to delete user');
      }

      return await response.json();
    } catch (error) {
      console.error('Error deleting user:', error);
      throw error;
    }
  };

  useEffect(() => {
    const checkAccess = async () => {
      const user = auth.currentUser;
      if (!user) {
        router.push(`/${lang}/auth/signin`);
        return;
      }

      try {
        // For now, we'll use the direct service call to check access
        // In production, you might want to verify this through an API
        const { getUserProfile } = await import('@/Firebase/firestore/services/UserService');
        const userProfile = await getUserProfile(user.uid);
        setCurrentUser(userProfile);

        if (!userProfile || userProfile.role !== UserRole.CONSULTANT) {
          toast({
            title: isRTL ? "غير مصرح" : "Unauthorized",
            description: isRTL ? "ليس لديك صلاحية للوصول إلى إدارة المستخدمين" : "You don't have permission to access user management",
            variant: "destructive",
          });
          router.push(`/${lang}/Thiqah/Home`);
          return;
        }

        await loadUsers();
      } catch (error) {
        console.error("Error checking access:", error);
        toast({
          title: isRTL ? "خطأ" : "Error",
          description: isRTL ? "حدث خطأ في التحقق من الصلاحيات" : "Error checking permissions",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    checkAccess();
  }, [lang, router, toast, isRTL]);

  const loadUsers = async () => {
    try {
      const allUsers = await fetchUsers();
      setUsers(allUsers);
      setFilteredUsers(allUsers);
    } catch (error) {
      console.error("Error loading users:", error);
      toast({
        title: isRTL ? "خطأ" : "Error",
        description: isRTL ? "فشل في تحميل المستخدمين" : "Failed to load users",
        variant: "destructive",
      });
    }
  };

  // Filter and search functionality
  useEffect(() => {
    let filtered = users;

    // Apply search filter
    if (searchTerm) {
      filtered = filtered.filter(user =>
        user.displayName?.toLowerCase().includes(searchTerm.toLowerCase()) ||
        user.email?.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    // Apply role filter
    if (roleFilter !== "all") {
      filtered = filtered.filter(user => user.role === roleFilter);
    }

    setFilteredUsers(filtered);
  }, [users, searchTerm, roleFilter]);

  // Handler functions
  const handleRoleUpdate = async (uid: string) => {
    if (!newRole) return;

    try {
      setActionLoading(uid);
      await updateUserRole(uid, newRole);
      await loadUsers();
      setEditingUser(null);
      setNewRole(null);

      toast({
        title: isRTL ? "تم التحديث" : "Updated",
        description: isRTL ? "تم تحديث دور المستخدم بنجاح" : "User role updated successfully",
        variant: "default",
      });
    } catch (error: any) {
      console.error("Error updating role:", error);
      toast({
        title: isRTL ? "خطأ" : "Error",
        description: error.message || (isRTL ? "فشل في تحديث دور المستخدم" : "Failed to update user role"),
        variant: "destructive",
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleCreateUser = async () => {
    try {
      setActionLoading('create');
      await createUser(newUserForm);
      await loadUsers();
      setShowAddDialog(false);
      setNewUserForm({
        email: "",
        displayName: "",
        role: UserRole.CLIENT,
        password: ""
      });

      toast({
        title: isRTL ? "تم الإنشاء" : "Created",
        description: isRTL ? "تم إنشاء المستخدم بنجاح" : "User created successfully",
        variant: "default",
      });
    } catch (error: any) {
      console.error("Error creating user:", error);
      toast({
        title: isRTL ? "خطأ" : "Error",
        description: error.message || (isRTL ? "فشل في إنشاء المستخدم" : "Failed to create user"),
        variant: "destructive",
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleToggleUserStatus = async (uid: string, disabled: boolean) => {
    try {
      setActionLoading(uid);
      await toggleUserStatus(uid, disabled);
      await loadUsers();

      toast({
        title: isRTL ? "تم التحديث" : "Updated",
        description: disabled
          ? (isRTL ? "تم تعطيل المستخدم" : "User disabled")
          : (isRTL ? "تم تفعيل المستخدم" : "User enabled"),
        variant: "default",
      });
    } catch (error: any) {
      console.error("Error toggling user status:", error);
      toast({
        title: isRTL ? "خطأ" : "Error",
        description: error.message || (isRTL ? "فشل في تحديث حالة المستخدم" : "Failed to update user status"),
        variant: "destructive",
      });
    } finally {
      setActionLoading(null);
    }
  };

  const handleDeleteUser = async (uid: string) => {
    try {
      setActionLoading(uid);
      await deleteUser(uid);
      await loadUsers();
      setShowDeleteDialog(null);

      toast({
        title: isRTL ? "تم الحذف" : "Deleted",
        description: isRTL ? "تم حذف المستخدم بنجاح" : "User deleted successfully",
        variant: "default",
      });
    } catch (error: any) {
      console.error("Error deleting user:", error);
      toast({
        title: isRTL ? "خطأ" : "Error",
        description: error.message || (isRTL ? "فشل في حذف المستخدم" : "Failed to delete user"),
        variant: "destructive",
      });
    } finally {
      setActionLoading(null);
    }
  };

  // Utility functions
  const getRoleBadgeColor = (role: UserRole) => {
    switch (role) {
      case UserRole.CONSULTANT:
        return "bg-purple-100 text-purple-800 border-purple-200";
      case UserRole.CLIENT:
        return "bg-blue-100 text-blue-800 border-blue-200";
      case UserRole.VIEWER:
        return "bg-gray-100 text-gray-800 border-gray-200";
      default:
        return "bg-gray-100 text-gray-800 border-gray-200";
    }
  };

  const getRoleDisplayName = (role: UserRole) => {
    switch (role) {
      case UserRole.CONSULTANT:
        return isRTL ? "مستشار" : "Consultant";
      case UserRole.CLIENT:
        return isRTL ? "عميل" : "Client";
      case UserRole.VIEWER:
        return isRTL ? "مشاهد" : "Viewer";
      default:
        return role;
    }
  };

  const getStatusBadgeColor = (disabled?: boolean) => {
    return disabled
      ? "bg-red-100 text-red-800 border-red-200"
      : "bg-green-100 text-green-800 border-green-200";
  };

  const getStatusDisplayName = (disabled?: boolean) => {
    return disabled
      ? (isRTL ? "معطل" : "Disabled")
      : (isRTL ? "نشط" : "Active");
  };

  if (loading) {
    return (
      <div className="min-h-screen bg-white flex items-center justify-center">
        <div className="flex flex-col items-center gap-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--brand-blue)]"></div>
          <div className="text-[var(--brand-dark-gray)] text-lg">
            {isRTL ? "جاري التحميل..." : "Loading..."}
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-white">
      {/* Professional Header with Thiqah Branding */}
      <div className="bg-gradient-to-r from-[var(--brand-blue)] to-[var(--brand-dark-gray)] text-white">
        <div className="container mx-auto px-6 py-8">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="bg-white/10 p-3 rounded-lg backdrop-blur-sm">
                <Users className="w-8 h-8" />
              </div>
              <div>
                <h1 className="text-3xl font-bold">
                  {isRTL ? "إدارة المستخدمين" : "User Management"}
                </h1>
                <p className="text-white/80 mt-1">
                  {isRTL ? "إدارة أدوار المستخدمين وصلاحياتهم" : "Manage user roles and permissions"}
                </p>
              </div>
            </div>
            <div className="flex items-center gap-3">
              <Image
                src="/thiqah-logo.png"
                alt="Thiqah Logo"
                width={60}
                height={60}
                className="opacity-80"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-8">
        {/* Controls Section */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 p-6 mb-6">
          <div className="flex flex-col lg:flex-row gap-4 items-start lg:items-center justify-between">
            <div className="flex flex-col sm:flex-row gap-4 flex-1">
              {/* Search */}
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <Input
                  placeholder={isRTL ? "البحث عن المستخدمين..." : "Search users..."}
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="pl-10 border-gray-300 focus:border-[var(--brand-blue)] focus:ring-[var(--brand-blue)]"
                />
              </div>

              {/* Role Filter */}
              <div className="flex items-center gap-2">
                <Filter className="w-4 h-4 text-gray-500" />
                <Select value={roleFilter} onValueChange={setRoleFilter}>
                  <SelectTrigger className="w-40 border-gray-300">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">
                      {isRTL ? "جميع الأدوار" : "All Roles"}
                    </SelectItem>
                    {Object.values(UserRole).map((role) => (
                      <SelectItem key={role} value={role}>
                        {getRoleDisplayName(role)}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            {/* Add User Button */}
            <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
              <DialogTrigger asChild>
                <Button className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90 text-white">
                  <Plus className="w-4 h-4 mr-2" />
                  {isRTL ? "إضافة مستخدم" : "Add User"}
                </Button>
              </DialogTrigger>
              <DialogContent className="sm:max-w-md">
                <DialogHeader>
                  <DialogTitle>
                    {isRTL ? "إضافة مستخدم جديد" : "Add New User"}
                  </DialogTitle>
                  <DialogDescription>
                    {isRTL ? "أدخل تفاصيل المستخدم الجديد" : "Enter the details for the new user"}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="email">
                      {isRTL ? "البريد الإلكتروني" : "Email"}
                    </Label>
                    <Input
                      id="email"
                      type="email"
                      value={newUserForm.email}
                      onChange={(e) => setNewUserForm({...newUserForm, email: e.target.value})}
                      placeholder={isRTL ? "أدخل البريد الإلكتروني" : "Enter email"}
                    />
                  </div>
                  <div>
                    <Label htmlFor="displayName">
                      {isRTL ? "الاسم" : "Display Name"}
                    </Label>
                    <Input
                      id="displayName"
                      value={newUserForm.displayName}
                      onChange={(e) => setNewUserForm({...newUserForm, displayName: e.target.value})}
                      placeholder={isRTL ? "أدخل الاسم" : "Enter display name"}
                    />
                  </div>
                  <div>
                    <Label htmlFor="password">
                      {isRTL ? "كلمة المرور" : "Password"}
                    </Label>
                    <Input
                      id="password"
                      type="password"
                      value={newUserForm.password}
                      onChange={(e) => setNewUserForm({...newUserForm, password: e.target.value})}
                      placeholder={isRTL ? "أدخل كلمة المرور" : "Enter password"}
                    />
                  </div>
                  <div>
                    <Label htmlFor="role">
                      {isRTL ? "الدور" : "Role"}
                    </Label>
                    <Select
                      value={newUserForm.role}
                      onValueChange={(value) => setNewUserForm({...newUserForm, role: value as UserRole})}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {Object.values(UserRole).map((role) => (
                          <SelectItem key={role} value={role}>
                            {getRoleDisplayName(role)}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>
                <DialogFooter>
                  <Button
                    variant="outline"
                    onClick={() => setShowAddDialog(false)}
                    disabled={actionLoading === 'create'}
                  >
                    {isRTL ? "إلغاء" : "Cancel"}
                  </Button>
                  <Button
                    onClick={handleCreateUser}
                    disabled={actionLoading === 'create' || !newUserForm.email || !newUserForm.displayName || !newUserForm.password}
                    className="bg-[var(--brand-blue)] hover:bg-[var(--brand-blue)]/90"
                  >
                    {actionLoading === 'create' ? (
                      <div className="flex items-center gap-2">
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                        {isRTL ? "جاري الإنشاء..." : "Creating..."}
                      </div>
                    ) : (
                      isRTL ? "إنشاء" : "Create"
                    )}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Users Table */}
        <div className="bg-white rounded-lg shadow-sm border border-gray-200 overflow-hidden">
          <div className="px-6 py-4 border-b border-gray-200 bg-gray-50">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <Shield className="w-5 h-5 text-[var(--brand-blue)]" />
                <h2 className="text-lg font-semibold text-[var(--brand-dark-gray)]">
                  {isRTL ? "قائمة المستخدمين" : "Users List"}
                </h2>
                <Badge variant="secondary" className="ml-2">
                  {filteredUsers.length}
                </Badge>
              </div>
            </div>
          </div>

          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50">
                  <TableHead className="font-semibold text-[var(--brand-dark-gray)]">
                    {isRTL ? "المستخدم" : "User"}
                  </TableHead>
                  <TableHead className="font-semibold text-[var(--brand-dark-gray)]">
                    {isRTL ? "الدور" : "Role"}
                  </TableHead>
                  <TableHead className="font-semibold text-[var(--brand-dark-gray)]">
                    {isRTL ? "الحالة" : "Status"}
                  </TableHead>
                  <TableHead className="font-semibold text-[var(--brand-dark-gray)]">
                    {isRTL ? "تاريخ الإنشاء" : "Created"}
                  </TableHead>
                  <TableHead className="font-semibold text-[var(--brand-dark-gray)] text-right">
                    {isRTL ? "الإجراءات" : "Actions"}
                  </TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {filteredUsers.map((user) => (
                  <TableRow key={user.uid} className="hover:bg-gray-50">
                    <TableCell>
                      <div className="flex items-center gap-3">
                        <div className="w-10 h-10 rounded-full bg-[var(--brand-blue)]/10 flex items-center justify-center">
                          <span className="text-[var(--brand-blue)] font-semibold text-sm">
                            {user.displayName?.charAt(0)?.toUpperCase() || user.email?.charAt(0)?.toUpperCase()}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium text-[var(--brand-dark-gray)]">
                            {user.displayName || user.email}
                          </div>
                          <div className="text-sm text-gray-500">{user.email}</div>
                        </div>
                      </div>
                    </TableCell>
                    <TableCell>
                      {editingUser === user.uid ? (
                        <div className="flex items-center gap-2">
                          <Select
                            value={newRole || user.role}
                            onValueChange={(value) => setNewRole(value as UserRole)}
                          >
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {Object.values(UserRole).map((role) => (
                                <SelectItem key={role} value={role}>
                                  {getRoleDisplayName(role)}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                          <Button
                            size="sm"
                            onClick={() => handleRoleUpdate(user.uid)}
                            disabled={actionLoading === user.uid}
                            className="bg-green-600 hover:bg-green-700 text-white"
                          >
                            {actionLoading === user.uid ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                            ) : (
                              <Save className="w-4 h-4" />
                            )}
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => {
                              setEditingUser(null);
                              setNewRole(null);
                            }}
                            disabled={actionLoading === user.uid}
                          >
                            <X className="w-4 h-4" />
                          </Button>
                        </div>
                      ) : (
                        <Badge className={getRoleBadgeColor(user.role)}>
                          {getRoleDisplayName(user.role)}
                        </Badge>
                      )}
                    </TableCell>
                    <TableCell>
                      <Badge className={getStatusBadgeColor(user.disabled)}>
                        {getStatusDisplayName(user.disabled)}
                      </Badge>
                    </TableCell>
                    <TableCell className="text-sm text-gray-500">
                      {user.createdAt?.toDate?.()?.toLocaleDateString() || 'N/A'}
                    </TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button
                            variant="ghost"
                            size="sm"
                            disabled={actionLoading === user.uid}
                          >
                            {actionLoading === user.uid ? (
                              <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-gray-400"></div>
                            ) : (
                              <MoreVertical className="w-4 h-4" />
                            )}
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuLabel>
                            {isRTL ? "الإجراءات" : "Actions"}
                          </DropdownMenuLabel>
                          <DropdownMenuSeparator />

                          {user.uid !== currentUser?.uid && editingUser !== user.uid && (
                            <DropdownMenuItem
                              onClick={() => {
                                setEditingUser(user.uid);
                                setNewRole(user.role);
                              }}
                            >
                              <Edit className="w-4 h-4 mr-2" />
                              {isRTL ? "تعديل الدور" : "Edit Role"}
                            </DropdownMenuItem>
                          )}

                          {user.uid !== currentUser?.uid && (
                            <DropdownMenuItem
                              onClick={() => handleToggleUserStatus(user.uid, !user.disabled)}
                            >
                              {user.disabled ? (
                                <>
                                  <UserCheck className="w-4 h-4 mr-2" />
                                  {isRTL ? "تفعيل" : "Enable"}
                                </>
                              ) : (
                                <>
                                  <UserX className="w-4 h-4 mr-2" />
                                  {isRTL ? "تعطيل" : "Disable"}
                                </>
                              )}
                            </DropdownMenuItem>
                          )}

                          {user.uid !== currentUser?.uid && (
                            <DropdownMenuItem
                              onClick={() => setShowDeleteDialog(user.uid)}
                              className="text-red-600 focus:text-red-600"
                            >
                              <Trash2 className="w-4 h-4 mr-2" />
                              {isRTL ? "حذف" : "Delete"}
                            </DropdownMenuItem>
                          )}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>

          {filteredUsers.length === 0 && (
            <div className="text-center py-12">
              <Users className="w-12 h-12 text-gray-400 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                {isRTL ? "لا توجد مستخدمين" : "No users found"}
              </h3>
              <p className="text-gray-500">
                {isRTL ? "لم يتم العثور على مستخدمين مطابقين للبحث" : "No users match your search criteria"}
              </p>
            </div>
          )}
        </div>

        {/* Delete Confirmation Dialog */}
        <Dialog open={!!showDeleteDialog} onOpenChange={() => setShowDeleteDialog(null)}>
          <DialogContent>
            <DialogHeader>
              <DialogTitle className="text-red-600">
                {isRTL ? "تأكيد الحذف" : "Confirm Deletion"}
              </DialogTitle>
              <DialogDescription>
                {isRTL
                  ? "هل أنت متأكد من حذف هذا المستخدم؟ لا يمكن التراجع عن هذا الإجراء."
                  : "Are you sure you want to delete this user? This action cannot be undone."
                }
              </DialogDescription>
            </DialogHeader>
            <DialogFooter>
              <Button
                variant="outline"
                onClick={() => setShowDeleteDialog(null)}
                disabled={actionLoading === showDeleteDialog}
              >
                {isRTL ? "إلغاء" : "Cancel"}
              </Button>
              <Button
                variant="destructive"
                onClick={() => showDeleteDialog && handleDeleteUser(showDeleteDialog)}
                disabled={actionLoading === showDeleteDialog}
              >
                {actionLoading === showDeleteDialog ? (
                  <div className="flex items-center gap-2">
                    <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white"></div>
                    {isRTL ? "جاري الحذف..." : "Deleting..."}
                  </div>
                ) : (
                  isRTL ? "حذف" : "Delete"
                )}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </div>
  );
}
